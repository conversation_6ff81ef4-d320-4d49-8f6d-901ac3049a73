{"name": "doraepare", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-community/geolocation": "^3.0.5", "react": "19.1.0", "react-native": "0.80.1", "react-native-dotenv": "^3.4.8", "react-native-loading-spinner-overlay": "^3.0.1", "react-native-webview": "^11.26.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/cli": "^19.1.0", "@react-native-community/eslint-config": "^3.2.0", "@tsconfig/react-native": "^2.0.2", "@types/jest": "^29.2.1", "@types/node": "^18.16.1", "@types/react": "19.1.8", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "^0.73.8", "prettier": "^2.4.1", "react-test-renderer": "19.1.0", "typescript": "4.8.4"}, "jest": {"preset": "react-native"}}